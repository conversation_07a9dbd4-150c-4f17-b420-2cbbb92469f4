# CCS Theia Command Line Build Instructions

This document explains how to build the `bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang` project using CCS Theia command line tools.

## Prerequisites

1. **Code Composer Studio Theia** - Must be installed on your system
2. **TI MSPM0 SDK** - Required for the project dependencies
3. **Windows Command Prompt** - These scripts are designed for Windows

## Build Scripts

Simple batch files for CCS Theia:

### 1. `build.bat` - Simple Build (Recommended)
The simplest and most straightforward build script for CCS Theia.

```cmd
# Incremental build
build.bat

# Clean build artifacts
build.bat clean
```

**Features:**
- Automatic CCS Theia detection
- Simple incremental build and clean options
- Minimal configuration required
- Fast and reliable

## Output Files

After a successful build, you'll find:

- **Executable**: `Debug/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.out`
- **Map file**: `Debug/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.map`
- **Object files**: Various `.obj` files in the Debug directory

## Troubleshooting

### CCS Theia Not Found
If you get "CCS Theia not found" errors:

1. Check if CCS Theia is installed in one of these locations:
   - `C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe`
   - `C:\ti\ccstheia1220\ccs\eclipse\ccs-server-cli.exe`
   - `C:\ti\ccstheia1210\ccs\eclipse\ccs-server-cli.exe`
   - `C:\ti\ccstheia1200\ccs\eclipse\ccs-server-cli.exe`

2. If CCS Theia is installed elsewhere, modify the paths in `build.bat`

### Build Failures
Common solutions:

1. **Clean the project first**:
   ```cmd
   build.bat clean
   build.bat
   ```

2. **Check SDK installation**: Ensure TI MSPM0 SDK is properly installed

3. **Verify workspace**: Make sure you're running the script from the project root directory

4. **Check project configuration**: Ensure the `.project` and `.cproject` files are present

### SysConfig Issues
This project uses TI SysConfig. If you encounter SysConfig-related errors:

1. Ensure SysConfig is installed with CCS Theia
2. Check that the `.syscfg` file is present in the project
3. Verify SysConfig tool paths in CCS installation

## Manual CCS Theia Command Line

If you prefer to use CCS Theia command line directly:

```cmd
# Basic build command
"C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe" -noSplash -workspace "." -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang

# Clean build
"C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe" -noSplash -workspace "." -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.clean
```

## Integration with CI/CD

The build script can be integrated into continuous integration systems:

```cmd
# Example CI build command
call build.bat
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
```

## Additional Resources

- [CCS Theia User's Guide](https://software-dl.ti.com/ccs/esd/documents/users_guide_ccs_theia/index_command-line.html)
- [MSPM0 SDK Documentation](https://www.ti.com/tool/MSPM0-SDK)
- [BQ769x2 Reference Design](https://www.ti.com/tool/TIDA-010247)

## Notes

- This script is designed for Windows environments with CCS Theia
- For Linux/macOS, replace `.exe` with appropriate executables and adjust paths accordingly
- The project uses TI Clang compiler toolchain
- SysConfig files (`.syscfg`) are automatically processed during build
