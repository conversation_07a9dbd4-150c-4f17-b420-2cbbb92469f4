C:\ti\ccs2020\ccs\eclipse\ccs-server-cli.bat  -noSplash -workspace "C:\Users\<USER>\workspace_ccstheia\Test" -application com.ti.ccs.apps.importProject -ccs.location "C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang"
C:\ti\ccs2020\ccs\eclipse\ccs-server-cli.bat -noSplash -workspace "C:\Users\<USER>\workspace_ccstheia\Test" -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType clean
C:\ti\ccs2020\ccs\eclipse\ccs-server-cli.bat -noSplash -workspace "C:\Users\<USER>\workspace_ccstheia\Test" -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.configuration Debug