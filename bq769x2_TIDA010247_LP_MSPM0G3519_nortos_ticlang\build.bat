@echo off
REM ============================================================================
REM Simple CCS Theia Build Script
REM ============================================================================
REM Builds the bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang project
REM using CCS Theia command line tools
REM ============================================================================

setlocal

REM Project Configuration
set PROJECT_NAME=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
set WORKSPACE_DIR=%~dp0
set BUILD_CONFIG=Debug

REM Find CCS Theia installation
set CCS_CLI=C:\ti\ccs2020\ccs\eclipse\ccs-server-cli.bat

REM Check if CCS CLI exists
echo Building project with CCS Theia...
echo Project: %PROJECT_NAME%
echo Config: %BUILD_CONFIG%
echo.

REM Parse command line arguments
if "%1"=="clean" (
    echo Cleaning project...
    "%CCS_CLI%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.clean
) else (
    echo Building project...
    "%CCS_CLI%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG%
)

REM Check result
if %ERRORLEVEL% equ 0 (
    echo.
    echo *** BUILD SUCCESSFUL ***
    if not "%1"=="clean" (
        echo Output: %BUILD_CONFIG%\%PROJECT_NAME%.out
    )
) else (
    echo.
    echo *** BUILD FAILED ***
    echo Error code: %ERRORLEVEL%
    pause
)

endlocal
